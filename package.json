{"name": "sentrycoin-trading-engine", "version": "5.0.0", "description": "SentryCoin v5.0 'Apex Predator' - Multi-Strategy Market Intelligence Engine with Advanced Orchestration", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node tests/unit/core.test.js", "test:config": "node tests/config-test.js", "test:telegram": "node tests/telegram-test.js", "test:whale": "node tests/whale-watchlist-test.js", "test:whale:v2": "node tests/v2-multichain-whale-test.js", "test:spk:whales": "node tests/spk-whale-validation.js", "test:predatory": "node tests/predatory-system-test.js", "test:realworld": "node tests/real-world-transaction-test.js", "test:optimization": "node tests/wallet-centric-optimization-test.js", "test:integration": "node tests/integration/connectivity.test.js", "test:integration:predatory": "node tests/integration/predatory-whale-integration.test.js", "backtest": "node scripts/run-backtest.js", "backtest:fetch": "node scripts/run-backtest.js fetch", "backtest:test": "node scripts/run-backtest.js test", "backtest:quick": "node scripts/run-backtest.js quick", "backtest:v4": "node scripts/backtest-v4.js", "connectivity": "node tests/integration/connectivity.test.js", "build": "echo 'Production build - no compilation required'", "organize": "node scripts/organize-codebase.js", "uptime": "node scripts/uptime-monitor.js", "setup:v2": "node scripts/setup-v2-multichain.js", "fix:fantom": "node scripts/fix-fantom-error.js", "deploy:production": "node scripts/deploy-production.js", "monitor:production": "node monitoring/production-dashboard.js", "verify:deployment": "node verify-deployment.js", "uptime:monitor": "node uptime-pinger.js", "reports:session": "curl http://localhost:3000/reports/session", "reports:hourly": "curl http://localhost:3000/reports/hourly", "reports:daily": "curl http://localhost:3000/reports/daily", "reports:list": "curl http://localhost:3000/reports"}, "dependencies": {"ws": "^8.14.2", "node-telegram-bot-api": "^0.64.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["crypto", "flash-crash", "order-book", "quantitative", "trading", "binance", "market-microstructure", "liquidity-analysis"], "author": "Sentry<PERSON><PERSON>n", "license": "MIT", "engines": {"node": ">=18.0.0"}}