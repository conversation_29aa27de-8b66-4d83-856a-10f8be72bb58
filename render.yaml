# ===================================================================
# SentryCoin v5.1 "Apex Predator" - Render.com Production Deployment
# OPERATION UNWIND - 24/7 Cloud Deployment Configuration
# ===================================================================

services:
  - type: web
    name: sentrycoin-v5-apex-predator
    env: node
    plan: starter  # Upgraded from free for better performance
    region: oregon  # US West for optimal latency
    buildCommand: npm install --production
    startCommand: npm start
    healthCheckPath: /health

    # Auto-scaling configuration
    numInstances: 1

    # Resource allocation
    disk:
      name: sentrycoin-data
      mountPath: /opt/render/project/src/data
      sizeGB: 1

    # Environment variables for v5.1 deployment
    envVars:
      # === CORE SYSTEM CONFIGURATION ===
      - key: NODE_ENV
        value: production
      - key: LOG_LEVEL
        value: info
      - key: PORT
        value: 10000
      - key: EXCHANGE
        value: binance
      - key: PAPER_TRADING
        value: true

      # === V5.1 STRATEGY ORCHESTRATION ===
      - key: STRATEGY_CASCADE_HUNTER_ENABLED
        value: true
      - key: STRATEGY_COIL_WATCHER_ENABLED
        value: true
      - key: STRATEGY_SHAKEOUT_DETECTOR_ENABLED
        value: true
      - key: STRATEGY_ETH_UNWIND_ENABLED
        value: false  # Phase 1: Disabled

      # === STRATEGY PRIORITIES ===
      - key: STRATEGY_PRIORITY_ETH_UNWIND
        value: 10
      - key: STRATEGY_PRIORITY_CASCADE_HUNTER
        value: 7
      - key: STRATEGY_PRIORITY_COIL_WATCHER
        value: 3
      - key: STRATEGY_PRIORITY_SHAKEOUT_DETECTOR
        value: 3

      # === ETH_UNWIND CONFIGURATION ===
      - key: ETH_UNWIND_SYMBOL
        value: ETHUSDT
      - key: ETH_UNWIND_RESISTANCE_LEVEL
        value: 3800
      - key: ETH_UNWIND_SUPPORT_LEVEL
        value: 3600
      - key: ETH_UNWIND_PRIMARY_TARGET
        value: 3000
      - key: ETH_UNWIND_SECONDARY_TARGET
        value: 2800
      - key: ETH_UNWIND_USE_ATR_STOP
        value: true
      - key: ETH_UNWIND_ATR_PERIOD
        value: 14
      - key: ETH_UNWIND_ATR_MULTIPLIER
        value: 2.5
      - key: ETH_UNWIND_MAX_POSITION_SIZE
        value: 5.0
      - key: ETH_UNWIND_ARMED_THRESHOLD
        value: 6
      - key: ETH_UNWIND_EXECUTION_THRESHOLD
        value: 8

      # === CASCADE_HUNTER LEGACY SETTINGS ===
      - key: CASCADE_HUNTER_SYMBOL
        value: ETHUSDT
      - key: CASCADE_TRADING_ENABLED
        value: true
      - key: CASCADE_MAX_POSITION
        value: 500
      - key: CASCADE_STOP_LOSS
        value: 2.0
      - key: CASCADE_TAKE_PROFIT
        value: 5.0
      - key: CASCADE_PRESSURE_THRESHOLD
        value: 4.0
      - key: CASCADE_LIQUIDITY_THRESHOLD
        value: 500000
      - key: CASCADE_MOMENTUM_THRESHOLD
        value: -0.8

      # === ENHANCED DATA SERVICES ===
      - key: DERIVATIVES_MONITOR_ENABLED
        value: true
      - key: DERIVATIVES_MONITOR_INTERVAL
        value: 60000
      - key: ONCHAIN_MONITOR_V2_ENABLED
        value: true
      - key: ONCHAIN_MONITOR_V2_INTERVAL
        value: 300000

      # === RISK MANAGEMENT ===
      - key: MAX_ACTIVE_POSITIONS
        value: 3
      - key: SIGNAL_COOLDOWN_MINUTES
        value: 15
      - key: ENABLE_CONFLICT_VETO
        value: true
      - key: ENABLE_TRAILING_STOP_LOSS
        value: true
      - key: TRAIL_PROFIT_TRIGGER
        value: 1.5
      - key: TRAIL_DISTANCE
        value: 1.0

      # === SYSTEM MONITORING ===
      - key: COOLDOWN_MINUTES
        value: 5
      - key: ORDER_BOOK_DEPTH
        value: 50
      - key: CLOUD_STORAGE_ENABLED
        value: false

      # === SENSITIVE CREDENTIALS ===
      # IMPORTANT: Set these in Render Dashboard Environment Variables
      # DO NOT COMMIT THESE VALUES TO VERSION CONTROL
      # - TELEGRAM_BOT_TOKEN
      # - TELEGRAM_CHAT_ID
      # - TELEGRAM_ADMIN_CHAT_ID
      # - TELEGRAM_API_ID
      # - TELEGRAM_API_HASH
      # - ETHERSCAN_API_KEY
      # - ALPHA_VANTAGE_API_KEY
